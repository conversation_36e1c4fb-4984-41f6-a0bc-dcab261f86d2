/**
 * Test utility for blood request events
 * Use this in browser console to test the event system
 */

import bloodRequestEvents, { BLOOD_REQUEST_EVENTS, emitBloodRequestStatusUpdate } from './bloodRequestEvents';

// Test function to verify event system is working
export const testBloodRequestEvents = () => {
  // Subscribe to events
  const unsubscribe = bloodRequestEvents.subscribe(
    BLOOD_REQUEST_EVENTS.STATUS_UPDATED,
    (data) => {
      // Event received
    }
  );

  // Emit a test event
  emitBloodRequestStatusUpdate(123, 0, 1, { test: true });

  // Clean up
  setTimeout(() => {
    unsubscribe();
  }, 1000);
};

// Export for console testing
if (typeof window !== 'undefined') {
  window.testBloodRequestEvents = testBloodRequestEvents;
}
